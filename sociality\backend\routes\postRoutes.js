import express from "express";
import {
	createPost,
	deletePost,
	getPost,
	updatePost,
	likeUnlikePost,
	replyToPost,
	getFeedPosts,
	getForYouPosts,
	getFollowingPosts,
	getUserPosts,
	getUserReplies,
	getUserReposts,
	repostPost,
	replyToComment,
	likeUnlikeComment,
	deleteComment,
	getTrendingPostsHandler,
	markPostNotInterested,
} from "../controllers/postController.js";
import protectRoute from "../middlewares/protectRoute.js";

const router = express.Router();

// Specific routes MUST come before generic /:id routes
router.get("/feed", protectRoute, getFeedPosts); // Legacy route - redirects to for-you
router.get("/for-you", (req, res) => {
	console.log("🧪 [DEBUG] /for-you route hit directly!");
	res.json({ message: "For-you route working", timestamp: new Date().toISOString() });
}); // Temporary test without auth
router.get("/following", protectRoute, getFollowingPosts); // New Following feed
router.get("/trending", getTrendingPostsHandler); // Add route for trending posts

// Test route to verify routing works
router.get("/test-route", (req, res) => {
	console.log("🧪 [TEST] Test route hit successfully!");
	res.json({ message: "Test route working", timestamp: new Date().toISOString() });
});

// Simple test for for-you route without auth
router.get("/for-you-test", (req, res) => {
	console.log("🧪 [TEST] For-you test route hit successfully!");
	res.json({ message: "For-you test route working", timestamp: new Date().toISOString() });
});
router.get("/user/:username", getUserPosts);
router.get("/user/:username/replies", getUserReplies); // Add route for user replies
router.get("/user/:username/reposts", getUserReposts); // Add route for user reposts

// POST routes
router.post("/create", protectRoute, createPost);
// Debug route to test if routing works
router.post("/debug-not-interested", (req, res) => {
	console.log("🔍 DEBUG: not-interested route hit!");
	res.json({ message: "Debug route working", timestamp: new Date().toISOString() });
});
router.post("/not-interested/:postId", protectRoute, markPostNotInterested); // Must be before /:id routes
router.post("/like/:id", protectRoute, likeUnlikePost); // Change to POST for like
router.post("/reply/:id", protectRoute, replyToPost); // Change to POST for reply
router.post("/repost/:id", protectRoute, repostPost); // Change to POST for repost

// PUT routes
router.put("/comment/like/:postId/:commentId", protectRoute, likeUnlikeComment);
router.put("/reply/:postId/comment/:commentId", protectRoute, replyToComment);
router.put("/:id", protectRoute, updatePost); // Add update post route

// DELETE routes
router.delete("/comment/:postId/:commentId", protectRoute, deleteComment);
router.delete("/:id", protectRoute, deletePost);

// Generic /:id route MUST be last
router.get("/:id", getPost); // Get post by ID - must be after ALL other specific routes

export default router;
