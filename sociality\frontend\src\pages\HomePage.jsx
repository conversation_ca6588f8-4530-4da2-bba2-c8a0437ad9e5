import React from 'react';
import {
    <PERSON>,
    Flex,
    Spinner,
    useColorModeValue,
    Text,
    Tabs,
    TabList,
    Tab,
} from "@chakra-ui/react";
import { useEffect, useState } from "react";
import useShowToast from "../hooks/useShowToast";
import Post from "../components/post/Post";
import { useRecoilState, useRecoilValue } from "recoil";
import { postsAtom, userAtom } from "../atoms";
import SuggestedUsers from "../components/SuggestedUsers";
import CreatePost from "../components/CreatePost";
import { fetchWithSession } from "../utils/api";
import PokemonEmptyState from "../components/PokemonEmptyState";
import { postService } from "../services/api";
import "../components/NoBorderTab.css";

const HomePage = () => {
    const [posts, setPosts] = useRecoilState(postsAtom);
    const [loading, setLoading] = useState(true);
    const [activeTab, setActiveTab] = useState(0); // 0 = For You, 1 = Following
    const showToast = useShowToast();
    const user = useRecoilValue(userAtom);

    // Theme-aware colors
    const textColor = useColorModeValue("gray.800", "white");
    const spinnerColor = useColorModeValue("gray.600", "whiteAlpha.700");

    useEffect(() => {
        const getFeedPosts = async () => {
            setLoading(true);
            setPosts([]);
            try {
                let data;
                if (activeTab === 0) {
                    // For You feed
                    data = await postService.getForYouPosts();
                } else {
                    // Following feed
                    data = await postService.getFollowingPosts();
                }

                if (Array.isArray(data)) {
                    setPosts(data);
                } else {
                    showToast("Error", "Invalid data format", "error");
                }
            } catch (error) {
                console.error('Homepage posts fetch error:', error);

                // Handle different error types
                if (error.message.includes('401')) {
                    console.log('Authentication error on homepage, user might need to re-login');
                    showToast("Info", "Please refresh the page or log in again to see posts", "info");
                } else {
                    showToast("Error", error.message || 'Failed to fetch posts', "error");
                }
            } finally {
                setLoading(false);
            }
        };

        // Small delay for new users who just completed profile setup
        const delay = user && user.isProfileComplete ? 500 : 0;
        setTimeout(getFeedPosts, delay);
    }, [showToast, setPosts, user, activeTab]); // Added activeTab to dependencies

    // Handle tab change
    const handleTabChange = (index) => {
        setActiveTab(index);
    };

    const handlePostCreated = (newPost) => {
        setPosts((prevPosts) => [newPost, ...prevPosts]); // Add the new post to the top of the feed
    };

    return (
        <Flex gap="6" justify="center">
            {/* Main Content */}
            <Box flex={{ base: 1, md: 2 }} maxW="600px" position="relative" zIndex={1}>
                {/* Create Post Component */}
                <CreatePost onPostCreated={handlePostCreated} />

                {/* Feed Tabs */}
                <Box mb={6}>
                    <Tabs
                        index={activeTab}
                        onChange={handleTabChange}
                        variant='unstyled'
                        w="full"
                        border="none"
                        style={{ border: "none" }}
                        className="no-border-tabs"
                    >
                        <TabList justifyContent="center" border="none" style={{ border: "none" }} className="no-border-tablist" gap={4}>
                            <Tab
                                as="div"
                                borderRadius="md"
                                bg="rgba(0, 0, 0, 0.2)"
                                backdropFilter="blur(8px)"
                                boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
                                px={8}
                                py={3}
                                transition="all 0.3s ease"
                                border="none"
                                style={{
                                    border: "none",
                                    outline: "none",
                                    borderWidth: 0,
                                    borderStyle: "none"
                                }}
                                _selected={{
                                    bg: "rgba(0, 204, 133, 0.2)",
                                    boxShadow: "0 4px 18px rgba(0, 204, 133, 0.25)",
                                    border: "none",
                                    borderWidth: 0
                                }}
                                _hover={{
                                    bg: "rgba(0, 204, 133, 0.1)",
                                    boxShadow: "0 4px 15px rgba(0, 204, 133, 0.15)",
                                    border: "none",
                                    borderWidth: 0
                                }}
                                _focus={{
                                    border: "none",
                                    outline: "none",
                                    borderWidth: 0
                                }}
                                className="glass-tab no-border-tab"
                                color={textColor}
                                fontWeight="medium"
                            >
                                For you
                            </Tab>
                            <Tab
                                as="div"
                                borderRadius="md"
                                bg="rgba(0, 0, 0, 0.2)"
                                backdropFilter="blur(8px)"
                                boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
                                px={8}
                                py={3}
                                transition="all 0.3s ease"
                                border="none"
                                style={{
                                    border: "none",
                                    outline: "none",
                                    borderWidth: 0,
                                    borderStyle: "none"
                                }}
                                _selected={{
                                    bg: "rgba(0, 204, 133, 0.2)",
                                    boxShadow: "0 4px 18px rgba(0, 204, 133, 0.25)",
                                    border: "none",
                                    borderWidth: 0
                                }}
                                _hover={{
                                    bg: "rgba(0, 204, 133, 0.1)",
                                    boxShadow: "0 4px 15px rgba(0, 204, 133, 0.15)",
                                    border: "none",
                                    borderWidth: 0
                                }}
                                _focus={{
                                    border: "none",
                                    outline: "none",
                                    borderWidth: 0
                                }}
                                className="glass-tab no-border-tab"
                                color={textColor}
                                fontWeight="medium"
                            >
                                Following
                            </Tab>
                        </TabList>
                    </Tabs>
                </Box>

                {/* Posts */}
                {loading && (
                    <Flex justify="center" my="6">
                        <Spinner size="xl" color={spinnerColor} />
                    </Flex>
                )}

                {!loading && posts.length === 0 && (
                    <PokemonEmptyState
                        message={
                            activeTab === 0
                                ? "No posts to display. Try following some users or check back later!"
                                : "No posts from people you follow. Try following some users to see their posts here!"
                        }
                    />
                )}

                {Array.isArray(posts) &&
                    posts.map((post, index) => (
                        <React.Fragment key={post._id}>
                            <Post post={post} isPostPage={false} />
                            {index === 1 && ( // Show SuggestedUsers only after the second post (index 1)
                                <Box my="6">
                                    <SuggestedUsers />
                                </Box>
                            )}
                        </React.Fragment>
                    ))}

                {!loading && !Array.isArray(posts) && (
                    <Box textAlign="center" my="6">
                        <h1>Error loading posts. Please try again later.</h1>
                    </Box>
                )}
            </Box>

            {/* Suggested Users Section Removed */}
            {/* <Box
                flex={1}
                display={{ base: "none", md: "block" }}
                position="sticky"
                top="80px"
                maxH="80vh"
                overflowY="auto"
            >
                <SuggestedUsers />
            </Box> */}
        </Flex>
    );
};

export default HomePage;
